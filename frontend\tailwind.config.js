/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: "#ff7a35",
        secondary: "#fcd782",
        dark: "#1f1b18",
      },
      animation: {
        vibrate: 'vibrate 0.3s infinite',
        shake: 'shake 0.8s cubic-bezier(0.36, 0.07, 0.19, 0.97) both',
        shimmer: 'shimmer 2s infinite linear',
        fadeIn: 'fadeIn 1.5s ease-in-out'
      },
      keyframes: {
        vibrate: {
          '0%': { transform: 'translate(0)' },
          '25%': { transform: 'translate(-2px, 2px)' },
          '50%': { transform: 'translate(2px, -2px)' },
          '75%': { transform: 'translate(-2px, -2px)' },
          '100%': { transform: 'translate(2px, 2px)' }
        },
        shake: {
          '0%, 100%': { transform: 'translate(0)' },
          '10%, 30%, 50%, 70%, 90%': { transform: 'translate(-5px)' },
          '20%, 40%, 60%, 80%': { transform: 'translate(5px)' }
        },
        shimmer: {
          '0%': { backgroundPosition: '-200% 0' },
          '100%': { backgroundPosition: '200% 0' }
        },
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(10px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' }
        }
      },
      screens: {
        'xs': '430px',
        'mobile': {'max': '767px'},
        'tablet': {'min': '768px', 'max': '980px'},
        'desktop': '981px',
      },
      utilities: {
        '.scrollbar-hide': {
          /* IE and Edge */
          '-ms-overflow-style': 'none',
          /* Firefox */
          'scrollbar-width': 'none',
          /* Safari and Chrome */
          '&::-webkit-scrollbar': {
            display: 'none'
          }
        }
      }
    },
  },
  plugins: [],
}
