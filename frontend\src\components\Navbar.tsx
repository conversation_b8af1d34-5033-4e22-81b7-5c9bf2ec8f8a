import { useState, useEffect, useRef, useCallback } from 'react'; // Ajout de useRef et useCallback
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { UserPlus, LogIn, Menu, X, LogOut, Home, HelpCircle, User, ShieldAlert, Shield, ChevronDown, LifeBuoy, Bell, Mail, Maximize2, Bot } from 'lucide-react'; // Ajout de Bell, Mail, Maximize2, Bot
import { useAuth } from '../contexts/AuthContext';
import { useJobi } from '../contexts/JobiContext';
import { useSocket } from '../contexts/SocketContext'; // Importer useSocket
import { CoinIcon } from '../pages/dashboard/icons';
import api from '../services/api'; // Correction du nom d'import
import { Badge } from '@mui/material'; // Importer Badge de MUI
import DOMPurify from 'dompurify';
import { logger } from '@/utils/logger';
import { fetchCsrfToken } from '../services/csrf';
import { getCookie, setCookie } from '../utils/cookieUtils';
import { useAiCredits } from '../hooks/useAiCredits'; // Import du hook pour les crédits IA

// Interface pour les éléments du dropdown
interface DropdownItem {
  id: string;
  type: 'notification' | 'message';
  title: string;
  content: string;
  link: string;
  created_at: string;
  is_read: boolean;
  conversation_id?: string; // Pour les messages
}

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false); // Pour le menu mobile principal
  const [hasScrolled, setHasScrolled] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isCompactMode, setIsCompactMode] = useState(window.innerWidth < 980);
  const [isMobileMode, setIsMobileMode] = useState(window.innerWidth < 640);
  const [showUserMenu, setShowUserMenu] = useState(false); // Pour le menu utilisateur
  const [showNotificationDropdown, setShowNotificationDropdown] = useState(false); // Pour le dropdown de notifications
  const [totalUnreadCount, setTotalUnreadCount] = useState(0);
  const [dropdownItems, setDropdownItems] = useState<DropdownItem[]>([]);
  const [isLoadingDropdown, setIsLoadingDropdown] = useState(false);

  const { user, logout } = useAuth();
  const { socket } = useSocket(); // Utiliser le contexte Socket
  const navigate = useNavigate();
  const location = useLocation();
  const isDashboard = location.pathname.startsWith('/dashboard');
  const { balance, loading: jobiLoading } = useJobi();
  const { credits: aiCredits, loading: aiCreditsLoading } = useAiCredits(); // Hook pour les crédits IA
  const notificationBellRef = useRef<HTMLDivElement>(null); // Ref pour le dropdown

  // Référence pour stocker les handlers et éviter de recréer les écouteurs socket
  const socketHandlersRef = useRef<{
    handleUpdate: (eventName: string, payload?: any) => void;
  } | null>(null);

  // Fonction pour récupérer le compte combiné non lu
  const fetchUnreadCount = useCallback(async () => {
    if (user) {
      try {
        // Ajout du préfixe /api
        const response = await api.get<{ success: boolean; count: number }>('/api/notifications/combined-unread-count');
        if (response.data.success) {
          setTotalUnreadCount(response.data.count);
        }
      } catch (error) {
        logger.info("Erreur lors de la récupération du nombre total non lu:", error);  
      }
    } else {
      setTotalUnreadCount(0); // Reset count when logged out
    }
  }, [user]); // Dépend de l'utilisateur

  // Fetch initial unread count and set up polling interval
  useEffect(() => {
    fetchUnreadCount();
    const intervalId = setInterval(fetchUnreadCount, 60000); // Poll every minute as a fallback
    return () => clearInterval(intervalId);
  }, [fetchUnreadCount]); // Utiliser fetchUnreadCount comme dépendance

  // Setup WebSocket listeners for real-time updates
  useEffect(() => {
    if (socket && user) {
      // Créer un handler stable
      socketHandlersRef.current = {
        handleUpdate: (_eventName: string, _payload?: any) => {
          fetchUnreadCount(); // Refetch count on any relevant event
          if (showNotificationDropdown) {
            // Déclencher un re-fetch des items du dropdown
          }
        }
      };

      // Liste des événements à écouter
      const eventsToListen = [
        'new_message',
        'new_notification',
        'message_read',
        'notification_read',
        'conversation_unread',
        'message_deleted',
        'notification_deleted'
      ];

      // Abonnement aux événements
      eventsToListen.forEach(event => {
        socket.on(event, (payload) => socketHandlersRef.current?.handleUpdate(event, payload));
      });

      // Nettoyage des listeners
      return () => {
        const handlers = socketHandlersRef.current;
        if (handlers) {
          eventsToListen.forEach(event => {
            socket.off(event, handlers.handleUpdate);
          });
        }
      };
    }
  }, [socket, user, fetchUnreadCount, showNotificationDropdown]);



  // Fonction pour charger les données initiales
  const loadInitialData = useCallback(async () => {
    if (!user) return;

    setIsLoadingDropdown(true);
    setDropdownItems([]);

    try {
      // Charger notifications page 1
      const notifResponse = await api.get<{ success: boolean; data?: any[] }>('/api/notifications?unread=true&page=1');
      let notifications: DropdownItem[] = [];

      if (notifResponse.data.success && notifResponse.data.data && Array.isArray(notifResponse.data.data)) {
        notifications = notifResponse.data.data.map((n: any) => ({
          id: n.id,
          type: 'notification',
          title: n.title,
          content: n.content,
          link: n.link || `/dashboard/notifications#notif-${n.id}`,
          created_at: n.created_at,
          is_read: n.is_read,
        }));
      }

      // Charger messages
      let messages: DropdownItem[] = [];
      const msgResponse = await api.get<{ success: boolean; data?: { conversations?: any[] } }>('/api/messages?unread_only=true&limit=5');
      if (msgResponse.data.success && msgResponse.data.data && Array.isArray(msgResponse.data.data.conversations)) {
        messages = msgResponse.data.data.conversations.map((c: any) => ({
          id: c.last_message?.id || c.id,
          type: 'message',
          title: `Message de ${c.otherUser?.prenom || 'Utilisateur'}`,
          content: c.last_message_preview || 'Nouvelle conversation',
          link: `/dashboard/messages/${c.id}`,
          created_at: c.last_message_date || c.updated_at,
          is_read: false,
          conversation_id: c.id,
        }));
      }

      // Combiner et trier
      const allItems = [...notifications, ...messages]
        .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());


      setDropdownItems(allItems);

    } catch (error) {
      logger.info("Erreur lors du chargement initial:", error);
      setDropdownItems([]);
    } finally {
      setIsLoadingDropdown(false);
    }
  }, [user]);

  // Charger les données quand le dropdown s'ouvre
  useEffect(() => {
    if (showNotificationDropdown && user) {
      loadInitialData();
    }
  }, [showNotificationDropdown, user, loadInitialData]);


  // Scroll handling
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setHasScrolled(scrollPosition > 0);
      if (window.scrollY > 0) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    const handleResize = () => {
      setIsCompactMode(window.innerWidth < 980);
      setIsMobileMode(window.innerWidth < 640);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    if (location.hash) {
      setTimeout(() => {
        const element = document.getElementById(location.hash.slice(1));
        if (element) {
          const offset = 25;
          const elementPosition = element.getBoundingClientRect().top;
          const offsetPosition = elementPosition + window.scrollY - offset;
          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
          });
        }
      }, 10);
    }
  }, [location]);

  // Fermer les menus/dropdowns lorsqu'on clique ailleurs
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const targetElement = event.target as Element;
      // Fermer le menu utilisateur
      if (showUserMenu && !targetElement.closest('.user-menu-container')) {
        setShowUserMenu(false);
      }
      // Fermer le dropdown de notifications
      if (showNotificationDropdown && notificationBellRef.current && !notificationBellRef.current.contains(targetElement)) {
         setShowNotificationDropdown(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
    document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showUserMenu, showNotificationDropdown]);

  // Bloquer le scroll de l'arrière-plan quand le dropdown de notifications est ouvert
  useEffect(() => {
    const preventBodyScroll = (e: WheelEvent) => {
      const target = e.target as HTMLElement;
      const dropdown = document.querySelector('[data-notification-dropdown]');
      
      if (dropdown && dropdown.contains(target)) {
        // On est dans le dropdown, vérifier si on peut encore scroller
        const scrollableElement = dropdown.querySelector('.flex-1.overflow-y-auto') as HTMLElement;
        if (scrollableElement) {
          const { scrollTop, scrollHeight, clientHeight } = scrollableElement;
          const isAtTop = scrollTop === 0;
          const isAtBottom = scrollTop + clientHeight >= scrollHeight - 1; // -1 pour les arrondis
          
          // Si on est en haut et qu'on scroll vers le haut, OU en bas et qu'on scroll vers le bas
          if ((isAtTop && e.deltaY < 0) || (isAtBottom && e.deltaY > 0)) {
            e.preventDefault(); // Bloquer pour éviter le rebond vers la page
          }
        }
        return;
      }
      
      // On n'est pas dans le dropdown, bloquer complètement
      e.preventDefault();
    };

    if (showNotificationDropdown) {
      // Bloquer le scroll du body
      document.body.style.overflow = 'hidden';
      // Ajouter l'événement pour empêcher le scroll par la molette
      document.addEventListener('wheel', preventBodyScroll, { passive: false });
    } else {
      // Restaurer le scroll
      document.body.style.overflow = '';
      document.removeEventListener('wheel', preventBodyScroll);
    }

    // Cleanup au démontage
    return () => {
      document.body.style.overflow = '';
      document.removeEventListener('wheel', preventBodyScroll);
    };
  }, [showNotificationDropdown]);

  const handleLogout = async () => {
    await logout();
    navigate('/');
    setIsOpen(false);
    setShowUserMenu(false);
  };

  // --- Définition de la fonction et du composant AVANT le return principal ---

  // Handler for clicking on a dropdown item
  const handleDropdownItemClick = async (item: DropdownItem) => {
    setShowNotificationDropdown(false); // Close dropdown

    // Mark as read
    try {
      if (item.type === 'notification') {
        if (!item.is_read) {
           // Ajout /api
           await api.put(`/api/notifications/${item.id}/toggle-read`);
           fetchUnreadCount(); // Refetch count after marking as read
           // Mettre à jour l'état local pour refléter le changement immédiatement
           setDropdownItems(prevItems => prevItems.map(i => i.id === item.id ? { ...i, is_read: true } : i));
        }
      } else if (item.type === 'message' && item.conversation_id) {
        // Ajout /api
        await api.post(`/api/messages/${item.conversation_id}/read`, {}, {
          headers: {
            'X-CSRF-Token': await fetchCsrfToken(),
            'Content-Type': 'application/json'
          }
        });
        fetchUnreadCount(); // Refetch count after marking as read
         // Mettre à jour l'état local
         setDropdownItems(prevItems => prevItems.filter(i => !(i.type === 'message' && i.conversation_id === item.conversation_id)));
      }
    } catch (error) {
      logger.info("Erreur lors du marquage comme lu:", error);
      fetchUnreadCount(); // Refetch count even if marking failed, to be safe
    }

    // Navigate
    navigate(item.link);
  };



  // Composant Dropdown SIMPLE
  const NotificationDropdown = ({ items, isLoading, onClose, onItemClick }: {
    items: DropdownItem[],
    isLoading: boolean,
    onClose: () => void,
    onItemClick: (item: DropdownItem) => void
  }) => {
    // États pour le redimensionnement
    const [dimensions, setDimensions] = useState(() => {
      const savedWidth = getCookie('notification-dropdown-width');
      const savedHeight = getCookie('notification-dropdown-height');
      return {
        width: savedWidth ? parseInt(savedWidth) : (isMobileMode ? 288 : 320), // w-72 = 288px, w-80 = 320px
        height: savedHeight ? parseInt(savedHeight) : (isMobileMode ? 350 : 400)
      };
    });
    const [isResizing, setIsResizing] = useState(false);
    const startPosRef = useRef({ x: 0, y: 0 });
    const startSizeRef = useRef({ width: 0, height: 0 });

    // Fonctions de redimensionnement
    const handleMouseDown = useCallback((e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      setIsResizing(true);
      startPosRef.current = { x: e.clientX, y: e.clientY };
      startSizeRef.current = { width: dimensions.width, height: dimensions.height };
    }, [dimensions]);

    const handleMouseMove = useCallback((e: MouseEvent) => {
      if (!isResizing) return;

      const deltaX = e.clientX - startPosRef.current.x;
      const deltaY = e.clientY - startPosRef.current.y;

      // Position du bouton de notification
      const bellButton = notificationBellRef.current;
      if (!bellButton) return;
      
      const bellRect = bellButton.getBoundingClientRect();
      
      // Calculer les limites basées sur la taille de la fenêtre et la position
      // Sur mobile, prendre en compte le décalage de -mr-20 (80px)
      const mobileOffset = isMobileMode ? 80 : 0;
      const maxWidth = Math.min(800, window.innerWidth - 2, bellRect.right + mobileOffset - 2); // Ne pas dépasser le bord gauche
      const maxHeight = Math.min(800, window.innerHeight - 80); // 80px de marge pour la navbar

      // Redimensionnement depuis le coin bas gauche
      const newWidth = Math.max(250, Math.min(maxWidth, startSizeRef.current.width - deltaX));
      const newHeight = Math.max(200, Math.min(maxHeight, startSizeRef.current.height + deltaY));

      setDimensions({ width: newWidth, height: newHeight });
    }, [isResizing]);

    const handleMouseUp = useCallback(() => {
      if (isResizing) {
        setIsResizing(false);
        // Sauvegarder les dimensions dans les cookies
        setCookie('notification-dropdown-width', dimensions.width.toString(), 365 * 24 * 60 * 60); // 1 an
        setCookie('notification-dropdown-height', dimensions.height.toString(), 365 * 24 * 60 * 60); // 1 an
      }
    }, [isResizing, dimensions]);

    // Event listeners pour le redimensionnement
    useEffect(() => {
      if (isResizing) {
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = 'sw-resize';
        document.body.style.userSelect = 'none';
      } else {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      }

      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
      };
    }, [isResizing, handleMouseMove, handleMouseUp]);

    // Ajuster les dimensions et la position si la fenêtre du navigateur change de taille
    useEffect(() => {
      const handleResize = () => {
        // Position du bouton de notification
        const bellButton = notificationBellRef.current;
        if (!bellButton) return;
        
        const bellRect = bellButton.getBoundingClientRect();
        const maxWidth = Math.min(800, window.innerWidth - 2);
        const maxHeight = Math.min(800, window.innerHeight - 80);
        
        // Vérifier si le dropdown sortirait du bord gauche (en tenant compte du décalage mobile)
        const mobileOffset = isMobileMode ? 80 : 0;
        const wouldOverflowLeft = bellRect.right + mobileOffset - dimensions.width < 2;
        const adjustedMaxWidth = wouldOverflowLeft ? bellRect.right + mobileOffset - 2 : maxWidth;
        
        setDimensions(prev => ({
          width: Math.min(prev.width, adjustedMaxWidth),
          height: Math.min(prev.height, maxHeight)
        }));
      };

      window.addEventListener('resize', handleResize);
      handleResize(); // Vérifier immédiatement

      return () => {
        window.removeEventListener('resize', handleResize);
      };
    }, [dimensions.width]);



    return (
      <div 
        data-notification-dropdown
        className={`absolute right-0 mt-2 bg-white rounded-md shadow-lg border flex flex-col z-50 ${isMobileMode ? '-mr-20' : ''}`}
        style={{
          width: `${dimensions.width}px`,
          height: `${dimensions.height}px`,
          minWidth: '250px',
          minHeight: '200px',
          maxWidth: '800px',
          maxHeight: '800px',
          border: isResizing ? '2px solid #FF6B2C' : '1px solid #e5e7eb'
        }}
      >
        {/* Handle de redimensionnement */}
        <div
          className="absolute bottom-1 left-1 w-8 h-8 cursor-sw-resize z-30 bg-[#FF6B2C] bg-opacity-10 hover:bg-opacity-30 transition-all duration-200 rounded-full flex items-center justify-center border-2 border-[#FF6B2C] border-opacity-20 hover:border-opacity-60"
          onMouseDown={handleMouseDown}
          title="🔄 Redimensionner la fenêtre de notifications"
          style={{
            boxShadow: '0 2px 4px rgba(255, 107, 44, 0.2)'
          }}
        >
          <Maximize2 className="w-4 h-4 text-[#FF6B2C]" />
        </div>
        
        <div className="p-3 border-b border-gray-100 sticky top-0 bg-white">
          <h3 className="text-sm font-semibold text-gray-700">Notifications</h3>
        </div>
        <div className="flex-1 overflow-y-auto">
          {isLoading ? (
            <div className="p-4 text-center text-gray-500">Chargement...</div>
          ) : items.length === 0 ? (
            <div className="p-4 text-center text-gray-500 text-sm">Aucune nouvelle notification</div>
          ) : (
            <ul className="divide-y divide-gray-100">
              {items.map((item) => (
                <li key={item.id} className={`hover:bg-[#FFF8F3] ${!item.is_read && item.type === 'notification' ? 'bg-blue-50' : ''}`}>
                  <button
                    onClick={() => onItemClick(item)}
                    className="w-full text-left px-3 py-2.5 flex items-start space-x-2"
                  >
                    <div className={`mt-1 ${item.type === 'message' ? 'text-blue-500' : 'text-[#FF7A35]'}`}>
                      {item.type === 'message' ? <Mail className="h-4 w-4" /> : <Bell className="h-4 w-4" />}
                    </div>
                    <div className="flex-1">
                      <p className={`text-sm font-semibold text-gray-800 truncate ${!item.is_read && item.type === 'notification' ? 'font-bold' : ''}`}>{item.title}</p>
                      <p className="text-sm text-gray-600 line-clamp-2" dangerouslySetInnerHTML={{ __html: DOMPurify.sanitize(item.content) }}></p>
                      <p className="text-sm text-gray-400 mt-1">{new Date(item.created_at).toLocaleString('fr-FR', { day: '2-digit', month: 'short', hour: '2-digit', minute: '2-digit' })}</p>
                    </div>
                    {!item.is_read && item.type === 'notification' && (
                      <span className="h-2 w-2 bg-blue-500 rounded-full self-center mr-1"></span>
                    )}
                  </button>
                </li>
              ))}
            </ul>
          )}
        </div>
        <div className="p-2 border-t border-gray-100 text-center bg-white">
          <Link
            to="/dashboard/notifications"
            className="text-sm font-medium text-[#FF6B2C] hover:underline"
            onClick={onClose}
          >
            Voir toutes les notifications
          </Link>
        </div>
      </div>
    );
  };
  // --- Fin de la définition interne ---


  return (
    <>
      {/* Desktop and Mobile Navigation */}
      <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${hasScrolled ? 'bg-white shadow-md' : 'bg-transparent'}`}>
        <div className="w-full px-4 sm:px-6 lg:px-8">
          <div className={`flex justify-between items-center transition-all duration-300 ${isScrolled ? 'h-12 md:h-16' : 'h-16'}`}>
            {/* Logo */}
            <div className="flex-shrink-0">
              <Link to="/" className="flex items-center">
                <picture>
                  <source type="image/webp" srcSet="/images/logo_banniere_job_partiel_grand.webp" />
                  <source type="image/png" srcSet="/images/logo_banniere_job_partiel_grand.png" />
                  <img
                    src="/images/logo_banniere_job_partiel_grand.png"
                    alt="JobPartiel.fr"
                    className={`mt-0 transition-all duration-300 ${
                      isScrolled
                        ? 'h-[25px] sm:h-[25px] md:h-[35px] lg:h-[35px] xl:h-[40px]'
                        : 'h-[35px] sm:h-[35px] md:h-[50px] lg:h-[50px] xl:h-[50px]'
                    } w-auto`}
                    loading="lazy"
                  />
                </picture>
              </Link>
            </div>

            {/* Desktop Navigation Links - Visible seulement au-dessus de 980px */}
            {!isCompactMode && (
              <div className="flex items-center space-x-8">
                <div className="transition-all duration-500 flex items-center space-x-4">
                  {user && !isDashboard && (
                    <>
                      <Link to="/dashboard" className="bg-[#FF7A35] text-white px-6 py-1 rounded-md hover:bg-[#e55a20] transition-all hover:scale-105 font-medium flex items-center">
                        <Home className="h-4 w-4 mr-1" /> Accès dashboard
                      </Link>
                    </>
                  )}
                  {isDashboard && user && (
                    <Link to="/dashboard/profil" className="bg-[#FF7A35] text-white px-6 py-1 rounded-md hover:bg-[#e55a20] transition-all hover:scale-105 font-medium flex items-center">
                      <User className="h-4 w-4 mr-1" />
                      Mon Profil
                    </Link>
                  )}
                  {user && user.role === 'jobpadm' && ( // Ajout check user
                    <Link to="/admin" className="bg-[#FF6B2C] text-white px-6 py-1 rounded-md hover:bg-[#FF7A35] transition-all hover:scale-105 font-medium flex items-center">
                      <ShieldAlert className="h-4 w-4 mr-1" /> Admin
                    </Link>
                  )}
                  {user && user.role === 'jobmodo' && ( // Ajout check user
                    <Link to="/moderation" className="bg-blue-600 text-white px-6 py-1 rounded-md hover:bg-blue-700 transition-all hover:scale-105 font-medium flex items-center">
                      <Shield className="h-4 w-4 mr-1" /> Modo
                    </Link>
                  )}
                  {user ? (
                    <>
                      <button className="bg-red-600 text-white px-6 py-1 rounded-md hover:bg-red-700 transition-all hover:scale-105 font-medium flex items-center" onClick={handleLogout}>
                        <LogOut className="h-4 w-4 mr-1" /> Se déconnecter
                      </button>
                    </>
                  ) : (
                    <>
                      <div className="flex items-center space-x-2">
                        <Link to="/inscription" className="flex items-center space-x-2 text-white bg-[#FF7A35] border border-[#FF7A35] px-4 py-1.5 rounded-lg hover:bg-[#e55a20] transition-all hover:scale-105 font-medium text-sm">
                          <UserPlus className="h-4 w-4" />
                          <span>S'inscrire</span>
                        </Link>
                        <Link to="/login" className="flex items-center space-x-2 text-white bg-[#FF7A35] border border-[#FF7A35] px-4 py-1.5 rounded-lg hover:bg-[#e55a20] transition-all hover:scale-105 font-medium text-sm">
                          <LogIn className="h-4 w-4" />
                          <span>Connexion</span>
                        </Link>
                      </div>
                    </>
                  )}
                  {user && (
                    <div className="flex items-center space-x-2">
                      {/* Notification Bell & Dropdown */}
                      <div ref={notificationBellRef} className="relative">
                        <button
                          onClick={() => setShowNotificationDropdown(!showNotificationDropdown)}
                          className="p-2 rounded-full hover:bg-[#FFF8F3] transition-all duration-200 relative group"
                          aria-label="Notifications"
                        >
                          <Badge 
                            badgeContent={totalUnreadCount > 0 ? totalUnreadCount : null} 
                            color="error"
                            sx={{
                              '& .MuiBadge-badge': {
                                backgroundColor: '#FF6B2C',
                                color: 'white',
                                fontSize: '0.7rem',
                                fontWeight: '600',
                                minWidth: '16px',
                                height: '16px',
                                padding: '0 3px',
                                right: 2,
                                top: 2,
                                border: '1.5px solid white',
                                boxShadow: '0 0 0 1px #FF6B2C'
                              }
                            }}
                          >
                            <div className="relative">
                              <Bell className="h-5 w-5 text-[#FF7A35] group-hover:text-[#FF6B2C] transition-all duration-200 transform group-hover:scale-110" />
                              <div className="absolute inset-0 rounded-full bg-[#FF7A35] opacity-0 group-hover:opacity-10 transition-opacity duration-200" />
                            </div>
                          </Badge>
                        </button>
                        {showNotificationDropdown && (
                          <NotificationDropdown
                            items={dropdownItems}
                            isLoading={isLoadingDropdown}
                            onClose={() => setShowNotificationDropdown(false)}
                            onItemClick={handleDropdownItemClick}
                          />
                        )}
                      </div>
                      {/* Jobi Balance */}
                      <button
                        onClick={() => navigate('/dashboard/jobi')}
                        className="flex items-center space-x-1 px-3 py-2 rounded-lg hover:bg-[#FFF8F3] transition-colors"
                      >
                        <CoinIcon className="h-5 w-5 text-[#FF6B2C]" />
                        {!jobiLoading && (
                          <span className="text-sm font-medium">
                            {balance} Jobi
                          </span>
                        )}
                      </button>
                      {/* AI Credits */}
                      <button
                        onClick={() => navigate('/dashboard/ai-credits')}
                        className="flex items-center space-x-1 px-3 py-2 rounded-lg hover:bg-[#FFF8F3] transition-colors"
                      >
                        <Bot className="h-5 w-5 text-[#FF6B2C]" />
                        {!aiCreditsLoading && (
                          <span className="text-sm font-medium">
                            {aiCredits} IA
                          </span>
                        )}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Navigation pour écrans entre 640px et 980px */}
            {isCompactMode && !isMobileMode && (
              <div className="flex items-center space-x-2">
                {user && (
                  <div className="relative user-menu-container">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setShowUserMenu(!showUserMenu);
                      }}
                      className="flex items-center space-x-1 bg-[#FF7A35] text-white px-3 py-1.5 rounded-md hover:bg-[#e55a20] transition-colors"
                    >
                      <User className="h-4 w-4" />
                      <ChevronDown className="h-3 w-3" />
                    </button>

                    {showUserMenu && (
                      <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                        {isDashboard && (
                          <Link
                            to="/dashboard/profil"
                            className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-[#FFF8F3]"
                            onClick={() => setShowUserMenu(false)}
                          >
                            <User className="h-4 w-4 mr-2 text-[#FF7A35]" />
                            Mon Profil
                          </Link>
                        )}
                        {!isDashboard && (
                          <Link
                            to="/dashboard"
                            className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-[#FFF8F3]"
                            onClick={() => setShowUserMenu(false)}
                          >
                            <Home className="h-4 w-4 mr-2 text-[#FF7A35]" />
                            Dashboard
                          </Link>
                        )}
                        {user && user.role === 'jobpadm' && ( // Ajout check user
                          <Link
                            to="/admin"
                            className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-[#FFF8F3]"
                            onClick={() => setShowUserMenu(false)}
                          >
                            <ShieldAlert className="h-4 w-4 mr-2 text-[#FF6B2C]" />
                            Administration
                          </Link>
                        )}
                        {user && user.role === 'jobmodo' && ( // Ajout check user
                          <Link
                            to="/moderation"
                            className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-100"
                            onClick={() => setShowUserMenu(false)}
                          >
                            <Shield className="h-4 w-4 mr-2 text-blue-600" />
                            Modération
                          </Link>
                        )}
                        {user && (user.role === 'jobpadm' || user.role === 'jobmodo') && ( // Ajout check user
                          <Link
                            to="/support"
                            className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-orange-100"
                            onClick={() => setShowUserMenu(false)}
                          >
                            <LifeBuoy className="h-4 w-4 mr-2 text-orange-600" />
                            Support
                          </Link>
                        )}
                        <button
                          onClick={handleLogout}
                          className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-red-100 w-full text-left"
                        >
                          <LogOut className="h-4 w-4 mr-2 text-red-600" />
                          Se déconnecter
                        </button>
                      </div>
                    )}
                  </div>
                )}

                {user && (
                  <div className="flex items-center space-x-1">
                     {/* Notification Bell & Dropdown (Compact) */}
                     <div ref={notificationBellRef} className="relative">
                        <button
                          onClick={() => setShowNotificationDropdown(!showNotificationDropdown)}
                          className="p-1.5 rounded-full hover:bg-[#FFF8F3] transition-colors relative group"
                          aria-label="Notifications"
                        >
                          <Badge 
                            badgeContent={totalUnreadCount > 0 ? totalUnreadCount : null} 
                            color="error"
                            sx={{
                              '& .MuiBadge-badge': {
                                backgroundColor: '#FF6B2C',
                                color: 'white',
                                fontSize: '0.75rem',
                                fontWeight: 'bold',
                                minWidth: '18px',
                                height: '18px',
                                padding: '0 4px',
                                right: 3,
                                top: 3,
                                border: '2px solid white'
                              }
                            }}
                          >
                            <Bell className="h-4 w-4 text-[#FF7A35] group-hover:text-[#FF6B2C] transition-colors" />
                          </Badge>
                        </button>
                        {showNotificationDropdown && (
                          <NotificationDropdown
                            items={dropdownItems}
                            isLoading={isLoadingDropdown}
                            onClose={() => setShowNotificationDropdown(false)}
                            onItemClick={handleDropdownItemClick}
                          />
                        )}
                      </div>
                    {/* Jobi Balance (Compact) */}
                    <button
                      onClick={() => navigate('/dashboard/jobi')}
                      className="flex items-center space-x-1 px-1.5 py-1.5 rounded-lg hover:bg-[#FFF8F3] transition-colors"
                    >
                      <CoinIcon className="h-4 w-4 text-[#FF6B2C]" />
                      {!jobiLoading && (
                        <span className="text-sm font-medium">
                          {balance}
                        </span>
                      )}
                    </button>
                    {/* AI Credits (Compact) */}
                    <button
                      onClick={() => navigate('/dashboard/ai-credits')}
                      className="flex items-center space-x-1 px-1.5 py-1.5 rounded-lg hover:bg-[#FFF8F3] transition-colors"
                    >
                      <Bot className="h-4 w-4 text-[#FF6B2C]" />
                      {!aiCreditsLoading && (
                        <span className="text-sm font-medium">
                          {aiCredits}
                        </span>
                      )}
                    </button>
                  </div>
                )}

                {!user && (
                  <>
                    <Link to="/inscription" className="flex items-center text-white bg-[#FF7A35] px-2 py-1 rounded-md hover:bg-[#e55a20] transition-all hover:scale-105 text-sm">
                      <UserPlus className="h-4 w-4" />
                    </Link>
                    <Link to="/login" className="flex items-center text-white bg-[#FF7A35] px-2 py-1 rounded-md hover:bg-[#e55a20] transition-all hover:scale-105 text-sm">
                      <LogIn className="h-4 w-4" />
                    </Link>
                  </>
                )}
              </div>
            )}

            {/* Mobile menu button - uniquement sur très petits écrans (< 640px) */}
            {isMobileMode && (
              <div className="flex items-center">
                <div className="flex items-center space-x-2 mr-2">
                  {!user && (
                    <>
                      <Link to="/inscription" className="flex items-center space-x-1 text-white bg-[#FF7A35] px-2 py-1 rounded-md hover:bg-[#e55a20] transition-all hover:scale-105 text-sm">
                        <UserPlus className="h-4 w-4" />
                      </Link>
                      <Link to="/login" className="flex items-center space-x-1 text-white bg-[#FF7A35] px-2 py-1 rounded-md hover:bg-[#e55a20] transition-all hover:scale-105 text-sm">
                        <LogIn className="h-4 w-4" />
                      </Link>
                    </>
                  )}
                  {user && (
                    <div className="flex items-center space-x-2">
                       {/* Notification Bell & Dropdown (Mobile) */}
                       <div ref={notificationBellRef} className="relative">
                        <button
                          onClick={() => setShowNotificationDropdown(!showNotificationDropdown)}
                          className="p-1.5 rounded-md hover:bg-[#FFF8F3] transition-colors relative group"
                          aria-label="Notifications"
                        >
                          <Badge
                            badgeContent={totalUnreadCount > 0 ? totalUnreadCount : null}
                            color="error"
                            sx={{
                              '& .MuiBadge-badge': {
                                backgroundColor: '#FF6B2C',
                                color: 'white',
                                fontSize: '0.75rem',
                                fontWeight: 'bold',
                                minWidth: '18px',
                                height: '18px',
                                padding: '0 4px',
                                right: 3,
                                top: 3,
                                border: '2px solid white'
                              }
                            }}
                          >
                            <Bell className="h-5 w-5 text-[#FF7A35] group-hover:text-[#FF6B2C] transition-colors" />
                          </Badge>
                        </button>
                        {showNotificationDropdown && (
                          <NotificationDropdown
                            items={dropdownItems}
                            isLoading={isLoadingDropdown}
                            onClose={() => setShowNotificationDropdown(false)}
                            onItemClick={handleDropdownItemClick}
                          />
                        )}
                      </div>
                      {/* Jobi Balance (Mobile) */}
                      <button
                        onClick={() => navigate('/dashboard/jobi')}
                        className="flex items-center space-x-1 px-1.5 py-1.5 rounded-lg hover:bg-[#FFF8F3] transition-colors"
                      >
                        <CoinIcon className="h-4 w-4 text-[#FF6B2C]" />
                        {!jobiLoading && (
                          <span className="text-xs font-medium">
                            {balance}
                          </span>
                        )}
                      </button>
                      {/* AI Credits (Mobile) */}
                      <button
                        onClick={() => navigate('/dashboard/ai-credits')}
                        className="flex items-center space-x-1 px-1.5 py-1.5 rounded-lg hover:bg-[#FFF8F3] transition-colors"
                      >
                        <Bot className="h-4 w-4 text-[#FF6B2C]" />
                        {!aiCreditsLoading && (
                          <span className="text-xs font-medium">
                            {aiCredits}
                          </span>
                        )}
                      </button>
                      {/* User Menu Button (Mobile) */}
                      <div className="relative user-menu-container">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setShowUserMenu(!showUserMenu);
                          }}
                          className="flex items-center text-white bg-[#FF7A35] px-2.5 py-1.5 rounded-md hover:bg-[#e55a20] transition-all"
                        >
                          <User className="h-4 w-4" />
                        </button>

                        {showUserMenu && (
                          <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50">
                            {isDashboard ? (
                              <Link
                                to="/dashboard/profil"
                                className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-[#FFF8F3]"
                                onClick={() => setShowUserMenu(false)}
                              >
                                <User className="h-4 w-4 mr-2 text-[#FF7A35]" />
                                Mon Profil
                              </Link>
                            ) : (
                              <Link
                                to="/dashboard"
                                className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-[#FFF8F3]"
                                onClick={() => setShowUserMenu(false)}
                              >
                                <Home className="h-4 w-4 mr-2 text-[#FF7A35]" />
                                Dashboard
                              </Link>
                            )}
                            {user && user.role === 'jobpadm' && ( // Ajout check user
                              <Link
                                to="/admin"
                                className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-[#FFF8F3]"
                                onClick={() => setShowUserMenu(false)}
                              >
                                <ShieldAlert className="h-4 w-4 mr-2 text-[#FF6B2C]" />
                                Administration
                              </Link>
                            )}
                            {user && user.role === 'jobmodo' && ( // Ajout check user
                              <Link
                                to="/moderation"
                                className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-blue-100"
                                onClick={() => setShowUserMenu(false)}
                              >
                                <Shield className="h-4 w-4 mr-2 text-blue-600" />
                                Modération
                              </Link>
                            )}
                            {/* Ajout check user pour le bouton support mobile si nécessaire */}
                            {user && (user.role === 'jobpadm' || user.role === 'jobmodo') && (
                              <Link
                                to="/support"
                                className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-orange-100"
                                onClick={() => setShowUserMenu(false)}
                              >
                                <LifeBuoy className="h-4 w-4 mr-2 text-orange-600" />
                                Support
                              </Link>
                            )}
                            <button
                              onClick={handleLogout}
                              className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-red-100 w-full text-left"
                            >
                              <LogOut className="h-4 w-4 mr-2 text-red-600" />
                              Se déconnecter
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </div>
                <button
                  onClick={() => setIsOpen(true)}
                  className="p-2 rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
                  aria-expanded={isOpen}
                  aria-label="Menu principal"
                >
                  <Menu className="h-6 w-6" />
                </button>
              </div>
            )}
          </div>
        </div>
      </nav>

      {/* Mobile and Tablet menu overlay */}
      <div
        className={`fixed inset-0 bg-black/50 backdrop-blur-sm transition-opacity duration-300 z-50 md:hidden ${
          isOpen ? 'opacity-100 pointer-events-auto' : 'opacity-0 pointer-events-none'
        }`}
        onClick={() => setIsOpen(false)}
      >
        <div
          className={`fixed inset-y-0 right-0 w-full max-w-[300px] bg-white shadow-xl transform transition-transform duration-300 ease-out ${
            isOpen ? 'translate-x-0' : 'translate-x-full'
          }`}
          onClick={(e) => e.stopPropagation()}
        >
          {/* Menu header */}
          <div className="p-4 border-b border-gray-100 flex justify-between items-center">
            <picture>
              <source type="image/webp" srcSet="/images/logo_banniere_job_partiel_grand.webp" />
              <source type="image/png" srcSet="/images/logo_banniere_job_partiel_grand.png" />
              <img
                src="/images/logo_banniere_job_partiel_grand.png"
                alt="JobPartiel.fr"
                className="h-8 w-auto"
              />
            </picture>
            <button
              onClick={() => setIsOpen(false)}
              className="p-2 rounded-full hover:bg-gray-100 transition-colors"
              aria-label="Fermer le menu"
            >
              <X className="h-6 w-6 text-gray-600" />
            </button>
          </div>

          {/* Menu items */}
          <div className="overflow-y-auto overscroll-contain h-[calc(100vh-5rem)]">
            <div className="p-4">
              {/* Section Menu */}
              <div className="mb-6">
                <h2 className="text-lg font-bold text-[#FF6B2C] mb-4">Menu</h2>
                <div className="space-y-2">
                  <a href="#home" className="flex items-center px-3 py-2 text-gray-700 hover:bg-[#FFF8F3] rounded-lg transition-colors" onClick={(e) => {
                    e.preventDefault();
                    setIsOpen(false);

                    const element = document.getElementById('home');
                    if (!element) {
                      navigate('/#home');
                      return;
                    }

                    const offset = 25;
                    const elementPosition = element.getBoundingClientRect().top;
                    const offsetPosition = elementPosition + window.scrollY - offset;

                    window.scrollTo({
                      top: offsetPosition,
                      behavior: 'smooth'
                    });
                  }}>
                    <Home className="h-5 w-5 mr-2 text-[#FF7A35]" />
                    <span>Accueil</span>
                  </a>
                  <a href="#how-it-works" className="flex items-center px-3 py-2 text-gray-700 hover:bg-[#FFF8F3] rounded-lg transition-colors" onClick={(e) => {
                    e.preventDefault();
                    setIsOpen(false);

                    const element = document.getElementById('how-it-works');
                    if (!element) {
                      navigate('/#how-it-works');
                      return;
                    }

                    const offset = 25;
                    const elementPosition = element.getBoundingClientRect().top;
                    const offsetPosition = elementPosition + window.scrollY - offset;

                    window.scrollTo({
                      top: offsetPosition,
                      behavior: 'smooth'
                    });
                  }}>
                    <HelpCircle className="h-5 w-5 mr-2 text-[#FF7A35]" />
                    <span>Comment ça marche</span>
                  </a>
                  <a href="#pricing" className="flex items-center px-3 py-2 text-gray-700 hover:bg-[#FFF8F3] rounded-lg transition-colors" onClick={(e) => {
                    e.preventDefault();
                    setIsOpen(false);

                    const element = document.getElementById('pricing');
                    if (!element) {
                      navigate('/#pricing');
                      return;
                    }

                    const offset = 25;
                    const elementPosition = element.getBoundingClientRect().top;
                    const offsetPosition = elementPosition + window.scrollY - offset;

                    window.scrollTo({
                      top: offsetPosition,
                      behavior: 'smooth'
                    });
                  }}>
                    <span className="h-5 w-5 mr-2 flex items-center justify-center text-[#FF7A35]">💰</span>
                    <span>Tarifs</span>
                  </a>
                </div>
              </div>

              {/* Section Administration (si administrateur ou modérateur) */}
              {user && (user.role === 'jobpadm' || user.role === 'jobmodo') && ( // Ajout check user
                <div className="mb-6">
                  <h2 className="text-lg font-bold text-[#FF6B2C] mb-4">Administration</h2>
                  <div className="space-y-2">
                    {user && user.role === 'jobpadm' && ( // Ajout check user
                      <Link to="/admin" className="flex items-center px-3 py-2 text-white bg-[#FF6B2C] rounded-lg hover:bg-[#FF7A35] transition-colors" onClick={() => setIsOpen(false)}>
                        <ShieldAlert className="h-5 w-5 mr-2" />
                        <span>Administration</span>
                      </Link>
                    )}
                    {user && user.role === 'jobmodo' && ( // Ajout check user
                      <Link to="/moderation" className="flex items-center px-3 py-2 text-white bg-blue-600 rounded-lg hover:bg-blue-700 transition-colors" onClick={() => setIsOpen(false)}>
                        <Shield className="h-5 w-5 mr-2" />
                        <span>Modération</span>
                      </Link>
                    )}
                  </div>
                </div>
              )}

              {/* Section Authentification */}
              <div>
                <h2 className="text-lg font-bold text-[#FF6B2C] mb-4">Authentification</h2>
                {user ? (
                  <div>
                    <button onClick={() => { navigate('/dashboard'); setIsOpen(false); }} className="w-full px-3 py-2 text-white bg-[#FF7A35] rounded-lg hover:bg-[#e55a20] transition-colors flex items-center mb-2">
                      <Home className="h-5 w-5 mr-2" />
                      <span>Accéder au tableau de bord</span>
                    </button>

                    {/* Section Soldes */}
                    <div className="mb-4 space-y-2">
                      <h3 className="text-sm font-semibold text-gray-600 mb-2">Mes soldes</h3>
                      <button onClick={() => { navigate('/dashboard/jobi'); setIsOpen(false); }} className="w-full px-3 py-2 bg-white border border-[#FF6B2C] text-[#FF6B2C] rounded-lg hover:bg-[#FFF8F3] transition-colors flex items-center justify-between">
                        <div className="flex items-center">
                          <CoinIcon className="h-4 w-4 mr-2" />
                          <span className="text-sm">Jobi</span>
                        </div>
                        {!jobiLoading && (
                          <span className="text-sm font-medium">{balance}</span>
                        )}
                      </button>
                      <button onClick={() => { navigate('/dashboard/ai-credits'); setIsOpen(false); }} className="w-full px-3 py-2 bg-white border border-[#FF6B2C] text-[#FF6B2C] rounded-lg hover:bg-[#FFF8F3] transition-colors flex items-center justify-between">
                        <div className="flex items-center">
                          <Bot className="h-4 w-4 mr-2" />
                          <span className="text-sm">Crédits IA</span>
                        </div>
                        {!aiCreditsLoading && (
                          <span className="text-sm font-medium">{aiCredits}</span>
                        )}
                      </button>
                    </div>

                    <button onClick={() => { handleLogout(); setIsOpen(false); }} className="w-full px-3 py-2 text-white bg-[#FF7A35] rounded-lg hover:bg-[#e55a20] transition-colors flex items-center">
                      <LogOut className="h-5 w-5 mr-2" />
                      <span>Se déconnecter</span>
                    </button>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Link to="/inscription" className="flex items-center w-full px-3 py-2 text-white bg-[#FF7A35] rounded-lg hover:bg-[#e55a20] transition-colors" onClick={() => setIsOpen(false)}>
                      <UserPlus className="h-5 w-5 mr-2" />
                      <span>S'inscrire</span>
                    </Link>
                    <Link to="/login" className="flex items-center w-full px-3 py-2 text-white bg-[#FF7A35] rounded-lg hover:bg-[#e55a20] transition-colors" onClick={() => setIsOpen(false)}>
                      <LogIn className="h-5 w-5 mr-2" />
                      <span>Connexion</span>
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Navbar;
